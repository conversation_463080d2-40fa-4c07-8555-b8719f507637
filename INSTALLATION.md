# دليل التثبيت والتشغيل - Facebook Multi-Account Manager V2.0.0

## متطلبات النظام

### الحد الأدنى
- **المعالج**: Intel Core i3 أو AMD Ryzen 3 أو أحدث
- **الذاكرة**: 4 جيجابايت RAM
- **التخزين**: 500 ميجابايت مساحة فارغة
- **نظام التشغيل**: 
  - Windows 10 أو أحدث
  - macOS 10.14 أو أحدث  
  - Ubuntu 18.04 أو أحدث

### المتطلبات المسبقة
- **Node.js** (الإصدار 16.0.0 أو أحدث)
- **npm** (يأتي مع Node.js)
- اتصال بالإنترنت لتحميل التبعيات

## خطوات التثبيت

### 1. تحميل وتثبيت Node.js

#### Windows:
1. <PERSON><PERSON><PERSON><PERSON> إلى https://nodejs.org/
2. حمل النسخة LTS (الموصى بها)
3. شغل ملف التثبيت واتبع التعليمات
4. أعد تشغيل الكمبيوتر

#### macOS:
```bash
# باستخدام Homebrew
brew install node

# أو حمل من الموقع الرسمي
# https://nodejs.org/
```

#### Linux (Ubuntu/Debian):
```bash
# تحديث قائمة الحزم
sudo apt update

# تثبيت Node.js و npm
sudo apt install nodejs npm

# التحقق من الإصدار
node --version
npm --version
```

### 2. تحميل البرنامج

#### الطريقة الأولى: تحميل مباشر
1. حمل ملف ZIP من المصدر
2. فك الضغط في مجلد مناسب
3. انتقل إلى مجلد البرنامج

#### الطريقة الثانية: Git Clone
```bash
git clone [repository-url]
cd "Form Facebook Program - V2.0.0"
```

### 3. تثبيت التبعيات

افتح Terminal/Command Prompt في مجلد البرنامج وشغل:

```bash
npm install
```

### 4. تشغيل البرنامج

#### Windows:
- انقر مرتين على ملف `start.bat`
- أو شغل في Command Prompt:
```cmd
npm start
```

#### macOS/Linux:
```bash
# اجعل ملف التشغيل قابل للتنفيذ
chmod +x start.sh

# شغل البرنامج
./start.sh
```

أو:
```bash
npm start
```

## حل المشاكل الشائعة

### مشكلة: "node is not recognized"
**الحل**: 
1. تأكد من تثبيت Node.js بشكل صحيح
2. أعد تشغيل Terminal/Command Prompt
3. أعد تشغيل الكمبيوتر إذا لزم الأمر

### مشكلة: "npm install fails"
**الحل**:
```bash
# امسح cache npm
npm cache clean --force

# احذف node_modules وأعد التثبيت
rm -rf node_modules
npm install
```

### مشكلة: "Permission denied" (Linux/macOS)
**الحل**:
```bash
# استخدم sudo للتثبيت العام
sudo npm install -g electron

# أو غير ملكية مجلد npm
sudo chown -R $(whoami) ~/.npm
```

### مشكلة: البرنامج لا يفتح
**الحل**:
1. تحقق من وجود ملف `src/main.js`
2. تأكد من تثبيت جميع التبعيات
3. شغل في وضع التطوير:
```bash
npm run dev
```

### مشكلة: شاشة بيضاء فارغة
**الحل**:
1. افتح Developer Tools (F12)
2. تحقق من وجود أخطاء في Console
3. تأكد من وجود ملفات CSS و JS

## التحديث

لتحديث البرنامج لإصدار جديد:

```bash
# احفظ نسخة احتياطية من البيانات
cp -r data data_backup

# حمل الإصدار الجديد
# استبدل الملفات القديمة بالجديدة (عدا مجلد data)

# أعد تثبيت التبعيات
npm install

# شغل البرنامج
npm start
```

## إلغاء التثبيت

لإزالة البرنامج بالكامل:

```bash
# احذف مجلد البرنامج
rm -rf "Form Facebook Program - V2.0.0"

# (اختياري) احذف Node.js إذا لم تعد تحتاجه
```

## الدعم الفني

إذا واجهت أي مشاكل:

📧 **البريد الإلكتروني**: <EMAIL>  
📱 **الهاتف**: 07727232639

عند طلب الدعم، يرجى تضمين:
- نظام التشغيل والإصدار
- إصدار Node.js (`node --version`)
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

---

**تم تطوير هذا البرنامج بواسطة علي عاجل خشان المحنّة**
