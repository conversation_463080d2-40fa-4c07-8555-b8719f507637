# Facebook Multi-Account Manager V2.0.0

## برنامج إدارة حسابات فيسبوك المتعددة

برنامج متطور لإدارة عدة حسابات فيسبوك من مكان واحد مع واجهة مستخدم عربية جميلة ومتجاوبة.

### المطور
**علي عاجل خشان المحنّة**
- البريد الإلكتروني: <EMAIL>
- الهاتف: ***********

### الميزات الرئيسية

#### 🔐 إدارة الحسابات
- إضافة وإدارة حسابات فيسبوك متعددة
- تشفير آمن لكلمات المرور
- اختبار الاتصال للحسابات
- عرض حالة الحسابات (متصل/غير متصل)

#### 📝 النشر الجماعي
- نشر محتوى موحد على عدة حسابات
- دعم الصور والفيديوهات
- استهداف مجموعات محددة
- خيارات النشر العام والستوري
- تحكم في أوقات التأخير بين المنشورات

#### 💬 التعليقات الجماعية
- تعليق موحد أو مختلف لكل حساب
- استهداف منشورات محددة
- دعم الوسائط في التعليقات
- تحكم في أوقات التأخير

#### 👥 طلبات الصداقة الجماعية
- إرسال طلبات صداقة لملفات شخصية محددة
- تحديد عدد الطلبات لكل حساب
- تحكم في أوقات التأخير

#### 🖥️ الشاشات الموحدة
- عرض شعارات جميع الحسابات
- صندوق رسائل موحد
- إدارة الرسائل من مكان واحد

#### 💾 الردود الجاهزة
- حفظ ردود وتعليقات جاهزة
- تصنيف الردود حسب الفئات
- استخدام سريع للردود المحفوظة

### التقنيات المستخدمة

- **Electron** - لتطبيقات سطح المكتب
- **JavaScript ES6+** - اللغة الأساسية
- **HTML5 & CSS3** - الواجهة الأمامية
- **SQLite3** - قاعدة البيانات المحلية (اختياري)
- **Font Awesome** - الأيقونات
- **Google Fonts** - خط Cairo العربي

### متطلبات التشغيل

- **Node.js** (الإصدار 16 أو أحدث)
- **npm** أو **yarn**
- نظام التشغيل: Windows, macOS, أو Linux

### التثبيت والتشغيل

#### 1. تحميل المشروع
```bash
git clone [repository-url]
cd "Form Facebook Program - V2.0.0"
```

#### 2. تثبيت التبعيات
```bash
npm install
```

#### 3. تشغيل البرنامج
```bash
npm start
```

أو للتطوير:
```bash
npm run dev
```

#### 4. بناء البرنامج للتوزيع
```bash
# لنظام Windows
npm run build-win

# لنظام macOS
npm run build-mac

# لنظام Linux
npm run build-linux

# لجميع الأنظمة
npm run build
```

### هيكل المشروع

```
Form Facebook Program - V2.0.0/
├── src/
│   ├── main.js              # الملف الرئيسي لـ Electron
│   ├── index.html           # الواجهة الرئيسية
│   ├── css/
│   │   ├── main.css         # الأنماط الرئيسية
│   │   ├── animations.css   # الرسوم المتحركة
│   │   └── components.css   # أنماط المكونات
│   └── js/
│       ├── main.js          # منطق التطبيق الرئيسي
│       ├── navigation.js    # إدارة التنقل والأقسام
│       └── database.js      # إدارة قاعدة البيانات
├── assets/                  # الصور والأيقونات
├── data/                    # ملفات البيانات المحلية
├── package.json
└── README.md
```

### الاستخدام

#### إضافة حساب جديد
1. انتقل إلى قسم "إدارة الحسابات"
2. اضغط على "إضافة حساب جديد"
3. أدخل البريد الإلكتروني وكلمة المرور
4. اختبر الاتصال (اختياري)
5. احفظ الحساب

#### النشر الجماعي
1. انتقل إلى قسم "النشر الجماعي"
2. اكتب محتوى المنشور
3. أضف الصور/الفيديوهات (اختياري)
4. أدخل روابط المجموعات المستهدفة
5. اضبط إعدادات النشر
6. ابدأ النشر

#### التعليق الجماعي
1. انتقل إلى قسم "التعليقات الجماعية"
2. أدخل رابط المنشور المستهدف
3. اكتب التعليق أو اختر تعليقات مختلفة
4. اضبط إعدادات التأخير
5. ابدأ التعليق

### الأمان والخصوصية

- جميع كلمات المرور مشفرة محلياً
- البيانات محفوظة على جهازك فقط
- لا يتم إرسال أي بيانات لخوادم خارجية
- استخدام تأخيرات عشوائية لتجنب الحظر

### الدعم والمساعدة

للحصول على الدعم أو الإبلاغ عن مشاكل:

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: ***********

### الترخيص

هذا البرنامج مرخص تحت رخصة MIT. يمكنك استخدامه وتعديله بحرية.

### إخلاء المسؤولية

هذا البرنامج مخصص للاستخدام التعليمي والشخصي. يرجى التأكد من الامتثال لشروط خدمة فيسبوك عند الاستخدام. المطور غير مسؤول عن أي استخدام غير قانوني أو انتهاك لشروط الخدمة.

---

**تم تطوير هذا البرنامج بواسطة علي عاجل خشان المحنّة**
