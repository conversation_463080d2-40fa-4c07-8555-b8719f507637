{"name": "facebook-multi-account-manager", "version": "2.0.0", "description": "برنامج إدارة حسابات فيسبوك المتعددة - Facebook Multi-Account Manager", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux"}, "keywords": ["facebook", "automation", "multi-account", "social-media", "posting", "management"], "author": {"name": "علي عاجل خشان المحنّة", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"fs-extra": "^11.1.1"}, "build": {"appId": "com.aliajil.facebook-manager", "productName": "Facebook Multi-Account Manager", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}